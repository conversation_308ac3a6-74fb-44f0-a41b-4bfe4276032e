<template>
  <div class="cash-situation-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "CashSituationChart",
  props: {},
  data() {
    return {
      myChart: null,
      chartData: [
        { name: "营业收入", value: 64738.21, type: "income" },
        { name: "营业成本", value: 90.0, type: "cost" },
        { name: "税金", value: 316.15, type: "cost" },
        { name: "费用", value: -6446.17, type: "cost" },
        { name: "其他", value: -31721.59, type: "cost" },
        { name: "所得税", value: 90.0, type: "cost" },
        { name: "净利润", value: 49.0, type: "cost" },
        { name: "折旧摊销", value: -1113.63, type: "cost" },
        { name: "营运资金", value: -1113.63, type: "cost" },
        { name: "其他", value: -1954.92, type: "cost" },
        { name: "经营现金流", value: 13540.7, type: "result" },
      ],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(this.$refs.chartBox);

      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          borderWidth: 0,
          textStyle: {
            color: "#FFFFFF",
            fontSize: 12,
          },
          formatter: (params) => {
            const data = params[0];
            const originalData = this.chartData[data.dataIndex];
            let content = `<div style="font-weight: bold; margin-bottom: 8px;">${originalData.name}</div>`;

            content += `<div style="display: flex; align-items: center;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${data.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span>金额: ${originalData.value} 万元</span>
            </div>`;

            return content;
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "15%",
          top: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: this.chartData.map((item) => item.name),
          axisLabel: {
            color: "#ACC2E2",
            fontSize: 10,
            interval: 0,
            rotate: 45,
            formatter: (value) => {
              if (value.length > 6) {
                return value.substring(0, 4) + "...";
              }
              return value;
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            color: "#ACC2E2",
            fontSize: 10,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.1)",
              type: "dashed",
            },
          },
        },
        series: [
          {
            type: "bar",
            data: this.chartData.map((item, index) => ({
              value: item.value,
              itemStyle: {
                color: this.getBarColor(item.type, index),
              },
            })),
            barWidth: "60%",
            label: {
              show: true,
              position: "top",
              color: "#FFFFFF",
              fontSize: 10,
              formatter: (params) => {
                const value = params.value;
                if (Math.abs(value) >= 10000) {
                  return (value / 10000).toFixed(1) + "万";
                }
                return value.toFixed(0);
              },
            },
          },
        ],
      };

      this.myChart.setOption(option);
    },

    getBarColor(type, index) {
      const colors = {
        income: "#00D4AA",
        cost: "#FF6B6B",
        result: "#248EFF",
      };
      return colors[type] || "#248EFF";
    },

    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.cash-situation-chart {
  width: 100%;

  .chart-box {
    width: 100%;
    height: 300px;
  }
}
</style>
