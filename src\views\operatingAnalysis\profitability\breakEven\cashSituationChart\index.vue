<template>
  <div class="cash-situation-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "CashSituationChart",
  props: {},
  data() {
    return {
      myChart: null,
      chartData: [
        { name: "油气销售收入", value: 4739.21, type: "income" },
        { name: "贸易收入", value: 0.0, type: "income" },
        { name: "其他收入", value: 10.15, type: "income" },
        { name: "作业费用", value: -346.19, type: "cost" },
        { name: "其他税金", value: -121.99, type: "cost" },
        { name: "特别收益金", value: 0.0, type: "income" },
        { name: "贸易成本", value: 0.0, type: "cost" },
        { name: "销售及管理费用", value: -133.43, type: "cost" },
        { name: "其他费用", value: -13.03, type: "cost" },
        { name: "当期所得税", value: -504.02, type: "cost" },
        { name: "经营现金流", value: 3630.7, type: "result" },
      ],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(this.$refs.chartBox);

      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          borderWidth: 0,
          textStyle: {
            color: "#FFFFFF",
            fontSize: 12,
          },
          formatter: (params) => {
            const data = params[0];
            const originalData = this.chartData[data.dataIndex];
            let content = `<div style="font-weight: bold; margin-bottom: 8px;">${originalData.name}</div>`;

            content += `<div style="display: flex; align-items: center;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${data.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span>金额: ${originalData.value} 百万</span>
            </div>`;

            return content;
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "15%",
          top: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: this.chartData.map((item) => item.name),
          axisLabel: {
            color: "#ACC2E2",
            fontSize: 10,
            interval: 0,
            rotate: 45,
            formatter: (value) => {
              if (value.length > 6) {
                return value.substring(0, 4) + "...";
              }
              return value;
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            color: "#ACC2E2",
            fontSize: 10,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.1)",
              type: "dashed",
            },
          },
        },
        series: [
          {
            type: "bar",
            data: this.chartData.map((item) => ({
              value: item.value,
              itemStyle: {
                color: this.getBarColor(item.type),
              },
            })),
            barWidth: "60%",
            label: {
              show: true,
              position: "top",
              color: "#FFFFFF",
              fontSize: 10,
              formatter: (params) => {
                const value = params.value;
                if (Math.abs(value) >= 10000) {
                  return (value / 10000).toFixed(1) + "万";
                }
                return value.toFixed(0);
              },
            },
          },
        ],
      };

      this.myChart.setOption(option);
    },

    getBarColor(type) {
      const colors = {
        income: "#00D4AA",
        cost: "#FF6B6B",
        result: "#248EFF",
      };
      return colors[type] || "#248EFF";
    },

    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.cash-situation-chart {
  width: 100%;

  .chart-box {
    width: 100%;
    height: 300px;
  }
}
</style>
