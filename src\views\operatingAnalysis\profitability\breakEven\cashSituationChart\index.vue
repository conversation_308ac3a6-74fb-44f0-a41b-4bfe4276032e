<template>
  <div class="cash-situation-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "CashSituationChart",
  props: {},
  data() {
    return {
      myChart: null,
      chartData: [
        { name: "油气销售收入", value: 4739.21, type: "start" },
        { name: "贸易收入", value: 0.0, type: "positive" },
        { name: "其他收入", value: 10.15, type: "positive" },
        { name: "作业费用", value: 346.19, type: "negative" },
        { name: "其他税金", value: 241.99, type: "negative" },
        { name: "特别收益金", value: 0.0, type: "positive" },
        { name: "贸易成本", value: 0.0, type: "negative" },
        { name: "销售及管理费用", value: 133.43, type: "negative" },
        { name: "其他费用", value: 13.03, type: "negative" },
        { name: "当期所得税", value: 504.02, type: "negative" },
        { name: "经营现金流", value: 230, type: "positive" },
      ],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(this.$refs.chartBox);

      // 计算瀑布图的累积值和显示值
      const processedData = this.processWaterfallData();

      const option = {
        backgroundColor: "transparent",
        legend: {
          data: ["增加", "减少", "汇总"],
          top: "5%",
          right: "center",
          orient: "horizontal",
          textStyle: {
            color: "#ACC2E2",
            fontSize: 12,
          },
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 20,
          icon: "rect",
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 12,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          formatter: this.tooltipFormatter,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "5%",
          top: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: processedData.categories,
          axisLabel: {
            color: "#ACC2E2",
            fontSize: 10,
            interval: 0,
            rotate: 45,
            formatter: (value) => {
              if (value.length > 6) {
                return value.substring(0, 4) + "...";
              }
              return value;
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            color: "#ACC2E2",
            fontSize: 10,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.1)",
              type: "dashed",
            },
          },
        },
        series: [
          // 隐藏的辅助系列，用于定位
          {
            name: "辅助",
            type: "bar",
            stack: "总量",
            itemStyle: {
              color: "transparent",
            },
            emphasis: {
              itemStyle: {
                color: "transparent",
              },
            },
            data: processedData.assistData,
            legendHoverLink: false,
            silent: true,
          },
          // 增加系列（蓝色）
          {
            name: "增加",
            type: "bar",
            stack: "总量",
            itemStyle: {
              color: "#248EFF",
            },
            label: {
              show: true,
              position: "top",
              color: "#FFFFFF",
              fontSize: 10,
              fontWeight: "bold",
              formatter: (params) => {
                if (params.value !== null && params.value !== undefined) {
                  return `+${Math.abs(params.value).toFixed(0)}`;
                }
                return "";
              },
            },
            data: processedData.increaseData,
          },
          // 减少系列（红色）
          {
            name: "减少",
            type: "bar",
            stack: "总量",
            itemStyle: {
              color: "#FF6B6B",
            },
            label: {
              show: true,
              position: "top",
              color: "#FFFFFF",
              fontSize: 10,
              fontWeight: "bold",
              formatter: (params) => {
                if (params.value !== null && params.value !== undefined) {
                  return `-${Math.abs(params.value).toFixed(0)}`;
                }
                return "";
              },
            },
            data: processedData.decreaseData,
          },
          // 汇总系列（灰色）
          {
            name: "汇总",
            type: "bar",
            stack: "总量",
            itemStyle: {
              color: "#8C8C8C",
            },
            label: {
              show: true,
              position: "top",
              color: "#FFFFFF",
              fontSize: 10,
              fontWeight: "bold",
              formatter: (params) => {
                if (params.value !== null && params.value !== undefined) {
                  return `${Math.abs(params.value).toFixed(0)}`;
                }
                return "";
              },
            },
            data: processedData.summaryData,
          },
        ],
      };

      this.myChart.setOption(option);
    },

    // 处理瀑布图数据
    processWaterfallData() {
      const categories = [];
      const assistData = [];
      const increaseData = [];
      const decreaseData = [];
      const summaryData = [];
      let cumulative = 0;

      this.chartData.forEach((item, index) => {
        categories.push(item.name);

        if (item.type === "start") {
          // 起始值
          assistData.push(0);
          increaseData.push(null);
          decreaseData.push(null);
          summaryData.push(item.value);
          cumulative = item.value;
        } else if (item.type === "end") {
          // 结束值 - 计算最终累积值
          const finalValue = cumulative;
          assistData.push(0);
          increaseData.push(null);
          decreaseData.push(null);
          summaryData.push(finalValue);
          // 更新chartData中的最终值以保持一致性
          this.chartData[index].value = finalValue;
        } else if (item.type === "positive") {
          // 正向影响
          assistData.push(cumulative);
          increaseData.push(item.value);
          decreaseData.push(null);
          summaryData.push(null);
          cumulative += item.value;
        } else if (item.type === "negative") {
          // 负向影响
          assistData.push(cumulative);
          increaseData.push(null);
          decreaseData.push(Math.abs(item.value));
          summaryData.push(null);
          cumulative += item.value;
        }
      });

      return {
        categories,
        assistData,
        increaseData,
        decreaseData,
        summaryData,
      };
    },

    // Tooltip格式化函数
    tooltipFormatter(params) {
      if (!params || params.length === 0) return '';

      const dataIndex = params[0].dataIndex;
      const originalData = this.chartData[dataIndex];

      let content = `<div style="margin-bottom: 8px; font-weight: bold;">${originalData.name}</div>`;

      // 找到有值的系列
      const validParam = params.find(p => p.value !== null && p.value !== undefined && p.seriesName !== "辅助");

      if (validParam) {
        if (originalData.type === "start" || originalData.type === "end") {
          content += `<div style="display: flex; align-items: center;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${validParam.color}; border-radius: 50%; margin-right: 8px;"></span>
            <span>金额: ${Math.abs(originalData.value).toFixed(0)} 百万</span>
          </div>`;
        } else {
          const sign = originalData.value > 0 ? "+" : "";
          content += `<div style="display: flex; align-items: center;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${validParam.color}; border-radius: 50%; margin-right: 8px;"></span>
            <span>影响: ${sign}${originalData.value.toFixed(0)} 百万</span>
          </div>`;
        }
      }

      return content;
    },

    getWaterfallColor(type) {
      const colors = {
        start: "#248EFF",
        positive: "#00D4AA",
        negative: "#FF6B6B",
        end: "#248EFF",
      };
      return colors[type] || "#248EFF";
    },

    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.cash-situation-chart {
  width: 100%;

  .chart-box {
    width: 100%;
    height: 300px;
  }
}
</style>
