const changeRouter = {
  path: "/operating",
  nameCN: "经营管理",
  component: () => import("@/views/operatingAnalysis/index.vue"),
  children: [
    {
      path: "situation",
      name: "situation",
      nameCN: "经营态势",
      component: () => import("@/views/operatingAnalysis/situation/index.vue"),
    },
    {
      path: "oilProduction",
      name: "oilProduction",
      nameCN: "油气产量",
      component: () =>
        import("@/views/operatingAnalysis/mainIncome/oilProduct/index.vue"),
    },
    {
      path: "oilPrice",
      name: "oilPrice",
      nameCN: "油价分析",
      component: () =>
        import("@/views/operatingAnalysis/mainIncome/oilPrice/index.vue"),
    },
    {
      path: "salesRevenue",
      name: "salesRevenue",
      nameCN: "销售收入",
      component: () =>
        import("@/views/operatingAnalysis/mainIncome/salesRevenue/index.vue"),
    },
    {
      path: "oilCost",
      name: "oilCost",
      nameCN: "桶油成本",
      component: () =>
        import("@/views/operatingAnalysis/costAndExpense/oilCost/index.vue"),
    },
    {
      path: "oepx",
      name: "oepx",
      nameCN: "OEPX费用",
      component: () =>
        import("@/views/operatingAnalysis/costAndExpense/oepx/index.vue"),
    },
    {
      path: "sga",
      name: "sga",
      nameCN: "SG&A费用",
      component: () =>
        import("@/views/operatingAnalysis/costAndExpense/sga/index.vue"),
    },
    {
      path: "profitAnalysis",
      name: "profitAnalysis",
      nameCN: "盈利分析",
      component: () =>
        import(
          "@/views/operatingAnalysis/profitability/profitAnalysis/index.vue"
        ),
    },
    {
      path: "breakEvenAnalysis",
      name: "breakEvenAnalysis",
      nameCN: "盈亏平衡分析",
      component: () =>
        import("@/views/operatingAnalysis/profitability/breakEven/index.vue"),
    },
  ],
};

export default changeRouter;
