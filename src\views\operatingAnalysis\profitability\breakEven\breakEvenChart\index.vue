<template>
  <div class="break-even-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "BreakEven<PERSON>hart",
  props: {},
  data() {
    return {
      myChart: null,
      chartData: {
        fields: ["YC13-1", "LS17-2", "LS25-1", "YC13-10", "WC16-2"],
        breakEvenPrice: [35.97, 25.18, 27.60, 95.70, 0.00], // 盈亏平衡价
        cashFlowPrice: [22.10, 15.83, 12.42, 60.29, 0.00] // 经营现金流临界价
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(this.$refs.chartBox);

      const option = {
        color: ["#248EFF", "#FF8C42"], // 蓝色和橙色
        legend: {
          data: ["盈亏平衡价", "经营现金流临界价"],
          top: "5%",
          right: "center",
          orient: "horizontal",
          textStyle: {
            color: "#ACC2E2",
            fontSize: 12,
          },
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 20,
          icon: "rect",
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;z-index: 9999;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          formatter: this.tooltipFormatter,
        },
        grid: {
          top: "15%",
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.chartData.fields,
            axisTick: {
              alignWithLabel: true,
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
                fontSize: 12,
              },
              interval: 0,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "right",
              padding: [0, 10, 0, 0],
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(172, 194, 226, 0.1)",
                type: "dashed",
              },
            },
            axisLine: {
              show: false,
            },
          },
        ],
        series: [
          // 盈亏平衡价 - 顶部装饰
          {
            name: "盈亏平衡价",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              show: true,
              position: "top",
              color: "#FFFFFF",
              fontSize: 10,
              fontWeight: "bold",
              formatter: (params) => {
                return `¥${params.value.toFixed(2)}`;
              },
            },
            data: this.chartData.breakEvenPrice,
          },
          // 盈亏平衡价 - 底部装饰
          {
            name: "盈亏平衡价",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, 5],
            z: 12,
            data: this.chartData.breakEvenPrice,
          },
          // 盈亏平衡价 - 主体柱子
          {
            name: "盈亏平衡价",
            type: "bar",
            itemStyle: {
              opacity: 0.7,
            },
            barWidth: "20",
            data: this.chartData.breakEvenPrice,
          },
          // 经营现金流临界价 - 顶部装饰
          {
            name: "经营现金流临界价",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              show: true,
              position: "top",
              color: "#FFFFFF",
              fontSize: 10,
              fontWeight: "bold",
              formatter: (params) => {
                return `¥${params.value.toFixed(2)}`;
              },
            },
            data: this.chartData.cashFlowPrice,
          },
          // 经营现金流临界价 - 底部装饰
          {
            name: "经营现金流临界价",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, 5],
            z: 12,
            data: this.chartData.cashFlowPrice,
          },
          // 经营现金流临界价 - 主体柱子
          {
            name: "经营现金流临界价",
            type: "bar",
            itemStyle: {
              opacity: 0.7,
            },
            barWidth: "20",
            data: this.chartData.cashFlowPrice,
          },
        ],
      };

      this.myChart.setOption(option);
    },

    tooltipFormatter(params) {
      if (!params || params.length === 0) return '';

      // 过滤出主体柱子的数据（排除装饰性的pictorialBar）
      const validParams = params.filter(p => p.seriesType === 'bar');

      if (validParams.length === 0) return '';

      const dataIndex = validParams[0].dataIndex;
      const fieldName = this.chartData.fields[dataIndex];

      let content = `<div style="margin-bottom: 8px; font-weight: bold;">${fieldName}</div>`;

      validParams.forEach(param => {
        const color = param.color;
        const seriesName = param.seriesName;
        const value = param.value;

        content += `<div style="display: flex; align-items: center; margin-bottom: 4px;">
          <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></span>
          <span>${seriesName}: ¥${value.toFixed(2)}</span>
        </div>`;
      });

      return content;
    },

    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    },

    // 更新图表数据的方法
    updateChartData(newData) {
      this.chartData = newData;
      if (this.myChart) {
        this.initChart();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.break-even-chart {
  width: 100%;
  .chart-box {
    width: 100%;
    height: 300px;
  }
}
</style>
