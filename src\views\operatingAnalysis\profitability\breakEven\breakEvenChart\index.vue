<template>
  <div class="break-even-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "BreakEven<PERSON><PERSON>",
  props: {},
  data() {
    return {
      myChart: null,
      chartData: [
        { name: "YC13-1", value: 535.97, type: "field" },
        { name: "LS17-2", value: 122.16, type: "field" },
        { name: "LS25-1", value: 64.83, type: "field" },
        { name: "YC13-10", value: 27.47, type: "field" },
        { name: "WC16-2", value: 112.47, type: "field" }
      ]
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(this.$refs.chartBox);

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(12, 15, 41, 0.9)',
          borderWidth: 0,
          textStyle: {
            color: '#FFFFFF',
            fontSize: 12
          },
          formatter: (params) => {
            const data = params[0];
            const originalData = this.chartData[data.dataIndex];
            let content = `<div style="font-weight: bold; margin-bottom: 8px;">${originalData.name}</div>`;
            
            content += `<div style="display: flex; align-items: center;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${data.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span>盈亏平衡分析: ${originalData.value}</span>
            </div>`;
            
            return content;
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.map(item => item.name),
          axisLabel: {
            color: '#ACC2E2',
            fontSize: 10,
            interval: 0
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(172, 194, 226, 0.2)'
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#ACC2E2',
            fontSize: 10
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(172, 194, 226, 0.2)'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(172, 194, 226, 0.1)',
              type: 'dashed'
            }
          }
        },
        series: [
          {
            type: 'bar',
            data: this.chartData.map((item, index) => ({
              value: item.value,
              itemStyle: {
                color: this.getBarColor(index)
              }
            })),
            barWidth: '60%',
            label: {
              show: true,
              position: 'top',
              color: '#FFFFFF',
              fontSize: 10,
              formatter: (params) => {
                return params.value.toFixed(2);
              }
            }
          }
        ]
      };

      this.myChart.setOption(option);
    },

    getBarColor(index) {
      const colors = ['#248EFF', '#00D4AA', '#FFB800', '#FF6B6B', '#9C88FF'];
      return colors[index % colors.length];
    },

    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.break-even-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-box {
    width: 100%;
    height: 300px;
    flex: 1;
  }
}
</style>
