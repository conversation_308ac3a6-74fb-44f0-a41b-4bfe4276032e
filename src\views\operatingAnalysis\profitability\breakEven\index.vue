<template>
    <div class="break-even">
        <div class="content-up">
            <div class="cashSituation">
                <chartBox :title="'现金情况分析-收入情况（单位：万元）'">
                    <CashSituationChart />
                </chartBox>
            </div>
            <div class="breakEven">
                <chartBox :title="'盈亏平衡分析'">
                    <BreakEvenChart />
                </chartBox>
            </div>
        </div>
        <div class="content-down">
             <div class="">
                <chartBox :title="'净销量区间分布'"></chartBox>
            </div>
            <div class="">
                <chartBox :title="'分油气田分布'"></chartBox>
            </div>
        </div>
    </div>
</template>
<script>
import CashSituationChart from "./cashSituationChart/index.vue";
import BreakEvenChart from "./breakEvenChart/index.vue";
import chartBox from "@/components/common/chartBox.vue";

export default {
    name: "BreakEven",
    components: {
        CashSituationChart,
        BreakEvenChart,
        chartBox
    }
}
</script>
<style lang="scss" scoped>
.break-even {
    .content-up {
        display: flex;
        justify-content: space-between;
        gap: 10px;

        .cashSituation {
            flex: 1;
            min-width: 0;
        }

        .breakEven {
            flex: 1;
            min-width: 0;
        }
    }

    .content-down {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        gap: 10px;

        > div {
            flex: 1;
            min-width: 0;
        }
    }
}
</style>