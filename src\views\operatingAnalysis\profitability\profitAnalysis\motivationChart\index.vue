<template>
  <div class="motivation-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "Motivation<PERSON><PERSON>",
  props: {},
  data() {
    return {
      myChart: null,
      // 瀑布图数据 - 根据图片中的数据
      chartData: [
        { name: "上年同期", value: 1219, type: "start" },
        { name: "价格影响", value: 91, type: "positive" },
        { name: "产量影响", value: 527, type: "positive" },
        { name: "OPEX", value: 24, type: "positive" },
        { name: "其他经营", value: 11, type: "positive" },
        { name: "DD&A", value: 179, type: "positive" },
        { name: "其他", value: 2, type: "positive" },
        { name: "SG&A", value: 300, type: "negative" },
        { name: "勘探费用", value: 297, type: "positive" },
        { name: "其他费用", value: 5, type: "positive" },
        { name: "其他收益", value: 21, type: "positive" },
        { name: "本年累计", value: 1895, type: "end" }
      ]
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
      this.addResizeListener();
    });
  },
  beforeDestroy() {
    this.removeResizeListener();
    if (this.myChart) {
      this.myChart.dispose();
    }
  },

  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(this.$refs.chartBox);
      
      // 计算瀑布图的累积值和显示值
      const processedData = this.processWaterfallData();
      
      const option = {
        backgroundColor: "transparent",
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          formatter: this.tooltipFormatter,
        },
        grid: {
          top: "5%",
          left: "2%",
          right: "5%",
          bottom: "0",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: processedData.categories,
          axisTick: {
            alignWithLabel: true,
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
              fontSize: 11,
            },
            interval: 0,
            rotate: 30,
            margin: 12,
            overflow: 'break',
            width: 60,
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
            },
            formatter: "{value}",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.1)",
              type: "dashed"
            }
          },
        },
        series: [
          // 隐藏的辅助系列，用于定位
          {
            name: "辅助",
            type: "bar",
            stack: "总量",
            itemStyle: {
              color: "transparent",
            },
            emphasis: {
              itemStyle: {
                color: "transparent",
              },
            },
            data: processedData.assistData,
            tooltip: {
              show: false,
            },
          },
          // 显示的柱状图系列
          {
            name: "税前利润",
            type: "bar",
            stack: "总量",
            barWidth: "40%",
            itemStyle: {
              color: (params) => {
                // 根据数据类型设置颜色，参考图片中的配色
                const item = this.chartData[params.dataIndex];
                if (item.type === "start" || item.type === "end") {
                  return "#248EFF"; // 起始值和结束值使用深蓝色
                } else if (item.type === "positive") {
                  return "#58CFFF"; // 正向影响使用浅蓝色
                } else {
                  return "#FF6B6B"; // 负向影响使用红色
                }
              },
              borderRadius: [2, 2, 2, 2],
            },
            label: {
              show: true,
              position: "top",
              color: "#FFFFFF",
              fontSize: 11,
              fontWeight: "bold",
              distance: 8,
              formatter: (params) => {
                const item = this.chartData[params.dataIndex];
                if (item.type === "start" || item.type === "end") {
                  return `${item.value}`;
                } else {
                  return item.value > 0 ? `+${item.value}` : `${item.value}`;
                }
              },
            },
            data: processedData.displayData,
          },
        ],
      };

      this.myChart.setOption(option);
    },

    // 处理瀑布图数据
    processWaterfallData() {
      const categories = [];
      const assistData = [];
      const displayData = [];
      let cumulative = 0;

      this.chartData.forEach((item, index) => {
        categories.push(item.name);

        if (item.type === "start") {
          // 起始值
          assistData.push(0);
          displayData.push(item.value);
          cumulative = item.value;
        } else if (item.type === "end") {
          // 结束值 - 计算最终累积值
          const finalValue = cumulative;
          assistData.push(0);
          displayData.push(finalValue);
          // 更新chartData中的最终值以保持一致性
          this.chartData[index].value = finalValue;
        } else {
          // 中间的增减值
          assistData.push(cumulative);
          displayData.push(item.value);
          cumulative += item.value;
        }
      });

      return {
        categories,
        assistData,
        displayData,
      };
    },

    // Tooltip格式化函数
    tooltipFormatter(params) {
      if (!params || params.length === 0) return '';
      
      const dataIndex = params[0].dataIndex;
      const originalData = this.chartData[dataIndex];
      
      let content = `<div style="margin-bottom: 8px; font-weight: bold;">${originalData.name}</div>`;
      
      if (originalData.type === "start" || originalData.type === "end") {
        content += `<div>数值: ${originalData.value} 百万元</div>`;
      } else {
        const prefix = originalData.value > 0 ? "+" : "";
        content += `<div>变化: ${prefix}${originalData.value} 百万元</div>`;
      }
      
      return content;
    },

    // 添加窗口大小变化监听
    addResizeListener() {
      this.resizeHandler = () => {
        if (this.myChart) {
          this.myChart.resize();
        }
      };
      window.addEventListener('resize', this.resizeHandler);
    },

    // 移除窗口大小变化监听
    removeResizeListener() {
      if (this.resizeHandler) {
        window.removeEventListener('resize', this.resizeHandler);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.motivation-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-box {
    width: 100%;
    height: 100%;
    flex: 1;
    min-height: 350px;
  }
}
</style>
