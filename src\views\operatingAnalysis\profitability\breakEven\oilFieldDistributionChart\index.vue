<template>
  <div class="oil-field-distribution-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "OilFieldDistributionChart",
  data() {
    return {
      chart: null,
      // 参考SalesChart的堆叠柱状图颜色方案
      stackColors: ["#3FB4FF", "#fb9352"],
      chartData: {
        // X轴分类数据 - 根据参考图片的油气田名称
        categories: ['气田平均', 'LS17-2', 'LS25-1', 'YC13-1', 'YC13-10', 'WC16-2'],
        // 数据系列 - 根据参考图片的数值
        seriesData: [
          {
            name: '税前桶油当量成本',
            data: [19.96, 11.82, 12.87, 15.12, 60.00, 0], // 蓝色部分数值
            stack: 'total'
          },
          {
            name: '税前桶油当量利润',
            data: [11.37, 39.68, 24.45, 22.71, -30.00, 0], // 橙色部分数值
            stack: 'total'
          }
        ]
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    // 监听窗口大小变化，实现响应式
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 清理事件监听器和图表实例
    window.removeEventListener('resize', this.handleResize);
    if (this.chart) {
      this.chart.dispose();
    }
  },
  methods: {
    initChart() {
      // 如果已有实例，先销毁
      if (this.chart) {
        this.chart.dispose();
      }

      this.chart = echarts.init(this.$refs.chartBox);

      const option = {
        backgroundColor: "transparent",
        legend: {
          data: ["税前桶油当量成本", "税前桶油当量利润"],
          top: "5%",
          right: "center",
          orient: "horizontal",
          textStyle: {
            color: "#ACC2E2",
            fontSize: 12,
          },
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 20,
          icon: "rect",
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;z-index: 9999;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          formatter: this.tooltipFormatter,
        },
        grid: {
          top: "15%",
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.chartData.categories,
            axisTick: {
              alignWithLabel: true,
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
                fontSize: 12,
              },
              interval: 0,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "$/BOE",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "right",
              padding: [0, 10, 0, 0],
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(172, 194, 226, 0.1)",
                type: "dashed",
              },
            },
            axisLine: {
              show: false,
            },
          },
        ],
        series: this.chartData.seriesData.map((item, index) => ({
          name: item.name,
          type: 'bar',
          stack: item.stack,
          data: item.data,
          itemStyle: {
            color: this.stackColors[index]
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (params) => {
              // 只显示非零值
              return params.value !== 0 ? params.value.toFixed(2) : '';
            },
            fontSize: 12,
            color: '#FFFFFF',
            fontWeight: 'bold'
          },
          barWidth: '60%'
        }))
      };

      // 设置图表配置
      this.chart.setOption(option);
    },

    tooltipFormatter(params) {
      if (!params || params.length === 0) return '';
      
      const dataIndex = params[0].dataIndex;
      const categoryName = this.chartData.categories[dataIndex];
      
      let content = `<div style="margin-bottom: 8px; font-weight: bold;">${categoryName}</div>`;
      
      params.forEach(param => {
        const color = param.color;
        const seriesName = param.seriesName;
        const value = param.value;
        
        if (value !== 0) {
          content += `<div style="display: flex; align-items: center; margin-bottom: 4px;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></span>
            <span>${seriesName}: $${value.toFixed(2)}/BOE</span>
          </div>`;
        }
      });
      
      return content;
    },

    handleResize() {
      // 响应式处理
      if (this.chart) {
        this.chart.resize();
      }
    },

    // 更新图表数据的方法
    updateChartData(newData) {
      if (this.chart && newData) {
        this.chartData = { ...this.chartData, ...newData };
        this.initChart();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.oil-field-distribution-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-box {
    width: 100%;
    flex: 1;
    min-height: 200px;
    max-height: 280px;
  }
}
</style>
