<template>
  <div class="profitAnalysis">
    <div class="content-left">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <CarouselBtn :buttons="buttons" />
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </chartBox>
      </div>

      <div class="motivation-box">
        <chartBox :title="'同比增减动因'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="month"
              @change="logChange('动因日期选择', $event)"
            />
          </template>
          <CarouselBtn :buttons="buttons" />
          <div class="motivation-content">
            <MotivationChart />
          </div>
        </chartBox>
      </div>
    </div>
    <div class="content-right">
      <div class="statistics-box">
        <chartBox :title="'分项统计'">
          <div class="table-box">
            <CommonTable
              :tableData="tableData"
              :colums="colums"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </chartBox>
      </div>
      <div class="target-execution">
        <chartBox :title="'目标执行情况'">
          <div class="target-content">
            <TargetExecutionChart />
          </div>
        </chartBox>
      </div>
      <div class="trend-change">
        <chartBox :title="'趋势变动'">
          <CarouselBtn :buttons="buttons" />
          <div class="trend-content">
            <TrendChangeChart />
          </div>
        </chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import CommonTable from "@/components/comTable/commonTable.vue";
import CarouselBtn from "../../components/CarouselBtn.vue";
import DatePicker from "@/views/businessAnalysis/ogw/DatePicker.vue";
import ItemCard from "../../components/ItemCard.vue";
import MotivationChart from "./motivationChart/index.vue";
import TargetExecutionChart from "./targetExecutionChart/index.vue";
import TrendChangeChart from "./trendChangeChart/index.vue";
export default {
  name: "ProfitAnalysis",
  components: {
    CommonTable,
    CarouselBtn,
    DatePicker,
    ItemCard,
    MotivationChart,
    TargetExecutionChart,
    TrendChangeChart,
  },
  data() {
    return {
      newDateValue: "",
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      cardData: [
        { title: "税前利润", value: "20", unit: "亿元" },
        { title: "净利润", value: "15", unit: "亿元" },
        { title: "经营现金流", value: "22", unit: "亿元" },
      ],
      colums: [
        { label: "指标", prop: "indicator" },
        { label: "本年累计", prop: "thisYear" },
        { label: "同期目标", prop: "target" },
        { label: "同期完成率", prop: "rate" },
        { label: "全年目标", prop: "fullYear" },
        { label: "全年完成率", prop: "fullRate" },
      ],
      tableData: [
        {
          indicator: "油气销售收入",
          thisYear: "100",
          target: "100",
          rate: "100",
          fullYear: "100",
          fullRate: "100",
        },
        {
          indicator: "油气销售收入",
          thisYear: "100",
          target: "100",
          rate: "100",
          fullYear: "100",
          fullRate: "100",
        },
        {
          indicator: "油气销售收入",
          thisYear: "100",
          target: "100",
          rate: "100",
          fullYear: "100",
          fullRate: "100",
        },
        {
          indicator: "油气销售收入",
          thisYear: "100",
          target: "100",
          rate: "100",
          fullYear: "100",
          fullRate: "100",
        },
        {
          indicator: "油气销售收入",
          thisYear: "100",
          target: "100",
          rate: "100",
          fullYear: "100",
          fullRate: "100",
        },
      ],
    };
  },
  methods: {
    logChange(type, value) {
      console.log(type, value);
    },
  },
};
</script>
<style lang="scss" scoped>
.profitAnalysis {
  display: flex;
  gap: 10px;
  .content-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 0;
    .main-indicators {
      .card-box {
        display: flex;
        justify-content: space-between;
        gap: 10px;
        flex: 1;
        margin: 8px 10px;
      }
    }

    .motivation-box {
      flex: 1.5;
      min-height: 400px;
      display: flex;
      flex-direction: column;

      .motivation-content {
        flex: 1;
        min-height: 350px;
        display: flex;
        flex-direction: column;
      }
    }
  }
  .content-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 0;

    .statistics-box {
      .table-box {
        margin: 12px 16px;
      }
    }

    .target-execution {
      flex: 1;
      min-height: 300px;
      display: flex;
      flex-direction: column;

      .target-content {
        margin: 12px 16px;
        flex: 1;
        min-height: 250px;
        display: flex;
        flex-direction: column;
      }
    }

    .trend-change {
      flex: 1;
      min-height: 300px;
      display: flex;
      flex-direction: column;

      .trend-content {
        margin: 12px 16px;
        flex: 1;
        min-height: 250px;
        display: flex;
        flex-direction: column;
      }
    }
  }
}
</style>
