<template>
  <div class="trend-change-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "TrendChangeChart",
  props: {},
  data() {
    return {
      mychart: null,
      // 图表数据 - 根据图片中的数据
      chartData: {
        months: ["1月", "2月", "3月", "4月", "5月", "6月"],
        currentYear: [95, 120, 150, 140, 130, 160], // 税前利润
        lastYear: [110, 115, 180, 145, 100, 155], // 去年同期
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    // 监听窗口大小变化，自动调整图表
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    if (this.mychart) {
      this.mychart.dispose();
    }
  },
  methods: {
    initChart() {
      this.mychart = echarts.init(this.$refs.chartBox);

      const option = {
        color: ["#248EFF", "#7262FD"], //圆柱体颜色
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;z-index: 9999;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          formatter: (params) => {
            if (!params || params.length === 0) return '';

            const axisValue = params[0].axisValue;
            let tooltipContent = `<div style="margin-bottom: 8px; font-weight: bold;">${axisValue}</div>`;

            // 按系列名称分组数据
            const seriesMap = new Map();
            params.forEach(param => {
              const seriesName = param.seriesName;
              if (!seriesMap.has(seriesName)) {
                seriesMap.set(seriesName, []);
              }
              seriesMap.get(seriesName).push(param);
            });

            // 生成tooltip内容
            seriesMap.forEach((seriesParams, seriesName) => {
              if (seriesParams.length > 0) {
                const param = seriesParams[0]; // 取第一个参数作为代表
                const value = param.value;
                const color = param.color;

                tooltipContent += `
                  <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></span>
                    <span style="margin-right: 8px;">${seriesName}:</span>
                    <span style="font-weight: bold;">${value} 万元</span>
                  </div>
                `;
              }
            });

            return tooltipContent;
          }
        },
        legend: {
          show: true,
          data: ['税前利润', '去年同期'],
          textStyle: {
            color: '#ACC2E2'
          },
          top: '2%',
          right: '5%'
        },
        grid: {
          top: "14%",
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.chartData.months,
            axisTick: {
              alignWithLabel: true,
            },
            nameTextStyle: {
              color: "#ACC2E2", //文本颜色
            },
            axisLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)", //轴线颜色
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2", //轴线文本颜色
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "万元",
            position: "left",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "right",
              padding: [0, 10, 0, 0],
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "税前利润",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.chartData.currentYear,
          },
          {
            name: "税前利润",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, 5],
            z: 12,
            data: this.chartData.currentYear,
          },
          {
            name: "税前利润",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.chartData.currentYear,
          },
          {
            name: "去年同期",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.chartData.lastYear,
          },
          {
            name: "去年同期",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, 5],
            z: 12,
            data: this.chartData.lastYear,
          },
          {
            name: "去年同期",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.chartData.lastYear,
          },
        ],
      };

      this.mychart.setOption(option);
    },

    handleResize() {
      if (this.mychart) {
        this.mychart.resize();
      }
    },

    // 更新图表数据的方法
    updateChartData(newData) {
      this.chartData = newData;
      if (this.mychart) {
        this.initChart();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.trend-change-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-box {
    width: 100%;
    flex: 1;
    min-height: 200px;
    max-height: 280px;
  }
}
</style>
